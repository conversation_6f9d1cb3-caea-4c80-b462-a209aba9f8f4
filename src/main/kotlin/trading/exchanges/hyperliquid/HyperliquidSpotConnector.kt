package trading.exchanges.hyperliquid

import io.micronaut.context.ApplicationContext
import io.micronaut.scheduling.TaskScheduler
import jakarta.annotation.PostConstruct
import jakarta.inject.Named
import jakarta.inject.Singleton
import trading.exceptions.scheduleCatching
import trading.exchanges.ExchangeConstants
import trading.models.*
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.utils.scale
import trading.utils.toSatoshies
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Collections.singletonList
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.min
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

@Singleton
class HyperliquidSpotConnector(
    @Named("hyperLiquidWallet") wallet: String,
    @Named("hyperLiquidKey") apiKey: String,
    hyperLiquidSigner: HyperLiquidSigner,
    hyperLiquidPrivateRestClient: HyperLiquidPrivateRestClient,
    hyperLiquidPublicRestClient: HyperLiquidPublicRestClient,
    telegramSender: TelegramSender,
    strategyRepository: StrategyRepository,
    context: ApplicationContext,
    taskScheduler: TaskScheduler,
) : HyperliquidBaseConnector(
    "hyperliquid-spot",
    apiKey,
    hyperLiquidPublicRestClient,
    telegramSender,
    hyperLiquidSigner,
    strategyRepository,
    wallet,
    context,
    hyperLiquidPrivateRestClient,
    taskScheduler,
) {

    private val universeCoinToSymbolMap: ConcurrentHashMap<UniverseCoin, FullSymbol> by lazy {
        ConcurrentHashMap(
            (getSupportedInstruments() as Map<FullSymbol, HyperliquidSpotConstant>).map { (symbol, constant) ->
                constant.universeCoin to symbol
            }.toMap()
        )
    }

    private val baseTokenIdToSymbolMap: ConcurrentHashMap<BaseTokenId, FullSymbol> by lazy {
        ConcurrentHashMap(
            (getSupportedInstruments() as Map<FullSymbol, HyperliquidSpotConstant>).map { (symbol, constant) ->
                constant.baseTokenId to symbol
            }.toMap()
        )
    }

    companion object {
        private const val SYMBOL_USDC = "USDC"

        private lateinit var instance: HyperliquidSpotConnector

        /**
         * Transforms universe coin @{index} to symbol name
         *
         * @throws IllegalStateException when no appropriate symbol name found for universe coin.
         */
        fun universeCoinToSymbol(coin: UniverseCoin): FullSymbol = instance.universeCoinToSymbolMap[coin] ?: error("Symbol was not found for universe coin \"$coin\"")
    }

    init {
        instance = this
    }

    @PostConstruct
    fun updateSymbolMaps() {
        if (!enabled) {
            return
        }
        taskScheduler.scheduleCatching(this, 1.minutes.toJavaDuration(), 1.minutes.toJavaDuration()) {
            (getSupportedInstruments() as Map<FullSymbol, HyperliquidSpotConstant>).forEach { (symbol, constant) ->
                universeCoinToSymbolMap[constant.universeCoin] = symbol
                baseTokenIdToSymbolMap[constant.baseTokenId] = symbol
            }
        }
    }

    override suspend fun getPositions(symbols: List<String>): List<ExPosition> = emptyList()

    override suspend fun getBalances(): List<ExBalance> = managed {
        hyperLiquidPublicRestClient.getSpotBalancesPositions(wallet)
    }.balances.map {
        ExBalance(name, it.token.toFullSymbol(), it.total)
    }

    override fun isMarginSupported(): Boolean = false

    override suspend fun getOrder(botOrderId: String, exOrderId: String?, symbol: String): FullOrder? =
        getOrderInternal<HLSpotOrder>(botOrderId, exOrderId, symbol)

    override suspend fun getOrders(symbol: FullSymbol, opened: Boolean?, count: Short): List<FullOrder> =
        if (opened != false) {
            managed { hyperLiquidPublicRestClient.getOpenOrders<HLSpotOrder>(wallet) }
                // we have to filter order using spot() helper as we received perps and spot orders
                .spot()
                .filter { it.symbolEx.symbol == symbol }
        } else {
            emptyList()
        }

    override suspend fun cancelAllOrders(symbols: List<FullSymbol>) {
        managed { hyperLiquidPublicRestClient.getOpenOrders<HLSpotOrder>(wallet) }
            // we have to filter order using spot() helper as we received perps and spot orders
            .spot()
            .forEach { cancelOrder(it.botOrderId, it.exOrderId, it.symbolEx.symbol) }
    }

    override suspend fun getInstrument(symbol: String): ExInstrument {
        val quote = getQuote(symbol)

        return ExInstrument(
            quote.symbolEx, quote.ask, quote.bid,
            0.0,
            Instant.now().truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS)
        )
    }

    override suspend fun getRiskLimits(symbol: String): List<ExRiskLimit> = singletonList(
        ExRiskLimit(
            Double.MAX_VALUE,
            leverage = 1 // hyperliquid spot does not support margin trading
        )
    )

    /**
     * We should add 10 000 to index to work with SPOT coins
     * See https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/asset-ids
     */
    override fun getAssetId(symbol: FullSymbol): Int = (getSupportedInstruments()[symbol] as HyperliquidSpotConstant).index + 10_000

    // https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/tick-and-lot-size
    // Prices can have up to 5 significant figures, but no more than
    // MAX_DECIMALS - szDecimals decimal places where MAX_DECIMALS
    // is 6 for perps and 8 for spot.
    override suspend fun getSupportedInstrumentsInternal(): Map<FullSymbol, ExchangeConstants> {
        val assets = getPreparedSpotAssetsContext()

        return assets.associate {
            it.token.name to HyperliquidSpotConstant(
                step = it.pricePrecision().toSatoshies(),
                min = it.token.szDecimals.toSatoshies(),
                it.universe.index,
                it.universe.baseTokenId,
                it.universe.name
            )
        }
    }

    private suspend fun getPreparedSpotAssetsContext(): List<SpotAsset>{
        val rawResp = managed{hyperLiquidPublicRestClient.getSpotAssetsCtxAndMeta()}

        val meta = rawResp.first() as Map<String, List<Map<String,Any>>>
        val marketData = rawResp.last() as List<Map<String,Any>>

        return meta["universe"]!!.map {
            val baseTokenId = (it["tokens"] as List<Int>).first()
            val index = it["index"].toString().toInt()

            SpotAsset(
                SpotAsset.Universe(
                    index,
                    it["name"].toString(),
                    baseTokenId
                ),
                SpotAsset.Token(
                    meta["tokens"]!![baseTokenId]["name"].toString(),
                    meta["tokens"]!![baseTokenId]["szDecimals"].toString().toInt(),
                ),
                SpotAsset.MarketData(
                    marketData[index]["markPx"].toString().toDoubleOrNull(),
                    marketData[index]["prevDayPx"].toString().toDoubleOrNull(),
                )
            )
        }
    }

    private data class SpotAsset(val universe: Universe, val token: Token, val marketData: MarketData) {
        data class Universe(val index: Int, val name: UniverseCoin, val baseTokenId: BaseTokenId)
        data class Token(val name: FullSymbol, val szDecimals: Int)
        data class MarketData(val markPx: Double? = null, val prevDayPx: Double? = null)

        private fun Double?.precision(): Int = this?.scale() ?: 10

        fun pricePrecision(): Int {
            val price = marketData.markPx ?: marketData.prevDayPx ?: 0.0
            // Increase the price by 5% to check if it's near precision border
            val increased = price * 1.05
            val precisionAdjuster = when {
                price.toInt().toString().length < increased.toInt().toString().length -> 1
                price < 1 && increased >= 1 -> 1
                else -> 0
            }

            return min(marketData.markPx.precision(), marketData.prevDayPx.precision()) - precisionAdjuster
        }
    }

    override fun universeCoinToSymbol(coin: UniverseCoin): FullSymbol = coin.toFullSymbol()
    override fun addSymbolNameToAssetId(err: String): String = err
    override fun symbolToUniverseCoin(symbol: FullSymbol): UniverseCoin = symbol.toUniverseCoin()

    /**
     * @return internal symbol name looking like `@{index}` for all symbols excepting `PURR/USDC`.
     */
    private fun FullSymbol.toUniverseCoin(): UniverseCoin = (getSupportedInstruments()[this] as HyperliquidSpotConstant).universeCoin

    /**
     * @return full symbol name like UBTC, UETH and so on.
     *
     * @throws IllegalStateException when no [FullSymbol] found by its internal symbol
     */
    private fun UniverseCoin.toFullSymbol(): FullSymbol {
        // USDC has no internal representation, so we should return this symbol as is.
        if (this == SYMBOL_USDC) {
            return this
        }

        return universeCoinToSymbolMap[this] ?: error("""Symbol was not found for universe coin "$this"""")
    }

    private fun BaseTokenId.toFullSymbol(): FullSymbol {
        if (this == 0) {
            return SYMBOL_USDC
        }

        return baseTokenIdToSymbolMap[this] ?: error("""Symbol is not found for base token id "$this"""")
    }
}

/**
 * @property index used to make assetId
 */
class HyperliquidSpotConstant(
    step: Double,
    min: Double,
    val index: Int,
    val baseTokenId: BaseTokenId,
    val universeCoin: UniverseCoin
) : ExchangeConstants(step, min)
