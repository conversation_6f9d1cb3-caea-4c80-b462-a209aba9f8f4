package trading.exchanges.hyperliquid

import io.micronaut.context.ApplicationContext
import io.micronaut.scheduling.TaskScheduler
import jakarta.inject.Named
import jakarta.inject.Singleton
import trading.exceptions.GoFurtherException
import trading.exchanges.ExchangeConstants
import trading.models.*
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.utils.scale
import trading.utils.toPrettyString
import trading.utils.toSatoshies
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Collections.singletonList
import kotlin.math.min

@Singleton
class HyperLiquidConnector(
    @Named("hyperLiquidWallet") wallet: String,
    @Named("hyperLiquidKey") apiKey: String,
    hyperLiquidSigner: HyperLiquidSigner,
    hyperLiquidPrivateRestClient: HyperLiquidPrivateRestClient,
    hyperLiquidPublicRestClient: HyperLiquidPublicRestClient,
    telegramSender: TelegramSender,
    strategyRepository: StrategyRepository,
    context: ApplicationContext,
    taskScheduler: TaskScheduler,
) : HyperliquidBaseConnector(
    "hyperliquid",
    apiKey,
    hyperLiquidPublicRestClient,
    telegramSender,
    hyperLiquidSigner,
    strategyRepository,
    wallet,
    context,
    hyperLiquidPrivateRestClient,
    taskScheduler,
) {

    // Pre-compiled regex to match asset=id within errors. We use pre-compiled
    // version because it is called within web sockets where performance is important.
    private val assetRegex = " asset=(\\d+)".toRegex()

    override suspend fun getPositions(symbols: List<String>): List<ExPosition> = managed {
        hyperLiquidPublicRestClient.getBalancesPositions(wallet)
    }.assetPositions.map {
        ExPosition(
            SymbolEx(name, it.position.coin),
            it.position.szi,
            it.position.entryPx,
            it.position.liquidationPx ?: 0.0
        )
    }

    override suspend fun getBalances(): List<ExBalance> = managed {
        hyperLiquidPublicRestClient.getBalancesPositions(wallet)
    }.crossMarginSummary.let {
        singletonList(ExBalance(name, "USDC", it.accountValue))
    }

    override fun isMarginSupported(): Boolean = true

    override suspend fun getMarginBalance(): Double = managed {
        hyperLiquidPublicRestClient.getBalancesPositions(wallet)
    }.crossMarginSummary.accountValue

    override suspend fun getOrder(botOrderId: String, exOrderId: String?, symbol: String): FullOrder? =
        getOrderInternal<HLOrder>(botOrderId, exOrderId, symbol)

    override suspend fun getOrders(symbol: FullSymbol, opened: Boolean?, count: Short): List<FullOrder> =
        if (opened != false) {
            managed { hyperLiquidPublicRestClient.getOpenOrders<HLOrder>(wallet) }
                // we have to filter order using perps() helper as we received perps and spot orders
                .perps()
                .filter { it.symbolEx.symbol == symbol }
        } else {
            emptyList()
        }

    override suspend fun cancelAllOrders(symbols: List<String>) {
        managed { hyperLiquidPublicRestClient.getOpenOrders<HLOrder>(wallet) }
            // we have to filter order using perps() helper as we received perps and spot orders
            .perps()
            .forEach { cancelOrder(it.botOrderId, it.exOrderId, it.symbolEx.symbol) }
    }

    override suspend fun getInstrument(symbol: String): ExInstrument {
        val any = managed { hyperLiquidPublicRestClient.getAssetCtxMeta() }
        val meta = (any.first() as Map<String, List<Map<String, Any>>>)["universe"]
        val ctx = any.last() as List<Map<String, Any>>

        val funding = meta?.mapIndexed { index, it ->
            if (it["name"] == symbol) ctx[index]["funding"].toString().toDouble() else null
        }?.filterNotNull()

        val quote = getQuote(symbol)

        return ExInstrument(
            quote.symbolEx, quote.ask, quote.bid,
            funding?.first() ?: 0.0,
            Instant.now().truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS)
        )
    }

    override suspend fun getRiskLimits(symbol: String): List<ExRiskLimit> = singletonList(
        ExRiskLimit(
            Double.MAX_VALUE,
            (getSupportedInstruments()[symbol] as HyperLiquidConstant).maxLeverage
        )
    )

    override suspend fun setLeverage(symbol: String, leverage: Int) {
        managed {
            hyperLiquidPrivateRestClient.updateLeverage(
                getAssetId(symbol),
                leverage
            )
        }
    }

    override fun getAssetId(symbol: String) = (getSupportedInstruments()[symbol] as HyperLiquidConstant).index
    private fun getSymbolName(assetId: Int): String? =
        getSupportedInstruments()
            .filter { (it.value as HyperLiquidConstant).index == assetId }
            .takeIf { it.isNotEmpty() }?.toList()?.first()?.first

    /**
     * Returns list of supported instruments
     *
     * The first version of calculating [HyperLiquidConstant.step] was made
     * according to the official documentation, but when launching Hyperliquid,
     * it started reporting mismatched ticks. We began taking the number of
     * ticks as in the mark price. This option worked for a while, but then
     * started to fail. We paid attention to the oracle price, which most
     * closely matched the trading price, and that worked.
     *
     * Hyperliquid likes to cut off trailing zeros, like if the price is 0.00101,
     * and then becomes 0.00100, it will send the price as 0.001, with precision
     * to 5 digits.
     */
    override suspend fun getSupportedInstrumentsInternal(): Map<FullSymbol, ExchangeConstants> {
        val meta = managed { hyperLiquidPublicRestClient.getAssetsMeta() }.universe

        val any = managed { hyperLiquidPublicRestClient.getAssetCtxMeta() }
        val ctx = any.last() as List<Map<String, Any>>

        fun precisionOfField(index: Int, name: String): Int {
            return ctx[index][name]?.toString()?.toDouble()?.scale() ?: 10
        }

        // If the price is close to precision border (i.e. from 9.99 to 10 or 0.99 to 1.0), we need to take smaller precision
        fun precisionAdjuster(index: Int): Int {
            val price =
                (ctx[index]["markPx"] ?: ctx[index]["prevDayPx"] ?: ctx[index]["oraclePx"])?.toString()?.toDouble()
                    ?: 0.0
            // Increase the price by 5% to check if it's near precision border
            val increased = price * 1.05
            return when {
                price.toInt().toString().length < increased.toInt().toString().length -> 1
                price < 1 && increased >= 1 -> 1
                else -> 0
            }
        }

        // https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/tick-and-lot-size
        return meta
            // transform elements to save indexation because of [ctx]
            .mapIndexed { index, el -> IndexedMeta(index, el) }
            .filterNot { it.meta.isDelisted == true } // leave only listed symbols
            .associate {
                // Always different prices in meta, taking minimal
                val precision = min(
                    min(
                        precisionOfField(it.index, "markPx"),
                        precisionOfField(it.index, "prevDayPx")
                    ),
                    precisionOfField(it.index, "oraclePx")
                ) - precisionAdjuster(it.index)

                it.meta.name to HyperLiquidConstant(
                    precision.toSatoshies(), it.meta.szDecimals.toSatoshies(), it.index, it.meta.maxLeverage
                )
            }
    }

    private data class IndexedMeta(val index: Int, val meta: HLUniverse)

    override fun adjustSymbol(symbol: String) = when (symbol) {
        "TSTBSC" -> "TST"
        "SOLAYER" -> "LAYER"
        "1000LUNC" -> "kLUNC"
        "1000SHIB" -> "kSHIB"
        "1000NEIRO", "1000NEIROCTO" -> "kNEIRO"
        "1000FLOKI" -> "kFLOKI"
        "1000DOGS" -> "kDOGS"
        "1000BONK" -> "kBONK"
        "1000PEPE" -> "kPEPE"
        else -> symbol
    }

    override fun universeCoinToSymbol(coin: UniverseCoin): FullSymbol = coin

    /**
     * Adds symbol name to asset id when it is presented within [err]
     */
    override fun addSymbolNameToAssetId(err: String): String {
        val matches = assetRegex.find(err) ?: return err

        if (matches.groupValues.count() < 2) {
            return err
        }

        val assetId = matches.groupValues[1].toInt()
        return getSymbolName(assetId)?.let { symbolName ->
            err.replaceFirst(
                " asset=$assetId",
                " asset=$assetId (${symbolName})"
            )
        } ?: err
    }

    override fun symbolToUniverseCoin(symbol: FullSymbol): UniverseCoin = symbol
}

class HyperLiquidConstant(
    step: Double, min: Double, val index: Int, val maxLeverage: Int
) : ExchangeConstants(step, min)

class TickSizeException(message: String) : GoFurtherException(false, message)

fun toTradeId(hash: String, oid: String, startPos: Double): String =
    (oid + hash.substring(2, 10) + startPos.toPrettyString())
