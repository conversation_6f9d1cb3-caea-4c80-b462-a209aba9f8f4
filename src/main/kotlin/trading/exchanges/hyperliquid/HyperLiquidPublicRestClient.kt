package trading.exchanges.hyperliquid

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.kittinunf.fuel.core.extensions.jsonBody
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import trading.exchanges.ApiResponse
import trading.exchanges.FuelRestClient
import trading.exchanges.hyperliquid.socket.HLFill
import trading.models.*
import java.time.Instant

@Singleton
class HyperLiquidPublicRestClient(
    @Value("\${hyperliquid.address}") address: String,
    objectMapper: ObjectMapper
) : FuelRestClient(
    address,
    objectMapper
) {

    suspend fun getCandles(
        coin: String,
        interval: Int,
        startTime: Long,
        endTime: Long
    ): ApiResponse<List<HLCandle>> = manager.post("/info")
        .jsonBody(
            "{\"type\":\"candleSnapshot\", " +
                    "\"req\": " +
                    "{\"coin\": \"$coin\", " +
                    "\"interval\": \"${interval}m\", " +
                    "\"startTime\": $startTime, " +
                    "\"endTime\": $endTime" +
                    "}}".trimIndent()
        )
        .execute()

    suspend fun getBalancesPositions(
        walletAddress: String
    ): ApiResponse<HLMarginSummaryResponse> = manager.post("/info")
        .jsonBody("{\"type\":\"clearinghouseState\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getSpotBalancesPositions(
        walletAddress: String
    ): ApiResponse<HLSpotBalancesResponse> = manager.post("/info")
        .jsonBody("{\"type\":\"spotClearinghouseState\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getAssetsMeta(): ApiResponse<HLAssets> = manager.post("/info")
        .jsonBody("{\"type\":\"meta\"}")
        .execute()

    suspend fun getSpotAssetsCtxAndMeta(): ApiResponse<List<Any>> = manager.post("/info")
        .jsonBody("""{"type":"spotMetaAndAssetCtxs"}""")
        .execute()

    suspend fun getAssetCtxMeta(): ApiResponse<List<Any>> = manager.post("/info")
        .jsonBody("{\"type\":\"metaAndAssetCtxs\"}")
        .execute()

    suspend fun getRateLimits(
        walletAddress: String
    ): ApiResponse<HLUsageStats> = manager.post("/info")
        .jsonBody("{\"type\":\"userRateLimit\",\"user\": \"$walletAddress\"}")
        .execute()

    suspend fun getOrderBook(
        coin: String
    ): ApiResponse<HLOrderBook> = manager.post("/info")
        .jsonBody("{\"type\":\"l2Book\",\"coin\": \"$coin\"}")
        .execute()

    /**
     * Returns list of both orders perps and spots
     */
    suspend fun <T : HLOrder> getOpenOrders(
        walletAddress: String
    ): ApiResponse<List<T>> = manager.post("/info")
        .jsonBody("{\"type\":\"openOrders\",\"user\": \"$walletAddress\"}")
        .execute()

    /**
     * Returns both order types perp and spot
     */
    suspend fun <T : HLOrder> getOrder(
        botOrderId: String,
        exOrderId: String?,
        walletAddress: String
    ): ApiResponse<HLNestedOrderWrapper<T>> = manager.post("/info")
        .jsonBody("{\"type\":\"orderStatus\",\"user\":\"$walletAddress\",\"oid\":${exOrderId ?: "\"$botOrderId\""}}")
        .execute()

    suspend fun getExecutions(
        walletAddress: String
    ): ApiResponse<List<HLFill>> = manager.post("/info")
        .jsonBody("{\"type\":\"userFills\",\"user\":\"$walletAddress\"}")
        .execute()
}

//region order status
data class HLOrderWrapper<T : HLOrder>(
    val order: T,
    val status: String //open, filled, canceled, triggered, rejected, marginCanceled
) {
    companion object {
        fun String.toOrdStatus() = when (this) {
            "open", "triggered" -> OrdStatus.New
            "filled" -> OrdStatus.Filled
            else -> OrdStatus.Canceled
        }
    }
}

data class HLNestedOrderWrapper<T: HLOrder>(
    val order: HLOrderWrapper<T>
)

/**
 * @param coin BTC, ETH for perps futures and @{index}, PURR/USDC for spot
 */
open class HLOrder(
    @JsonProperty("coin") coin: String,
    @JsonProperty("side") side: String,
    @JsonProperty("limitPx") price: Double,
    @JsonProperty("sz") size: Double,
    @JsonProperty("oid") orderId: Long,
    @JsonProperty("origSz") originalSize: Double,
    @JsonProperty("cloid") hexBotOrderId: String? = null
) : FullOrder(
    "" + orderId,
    SymbolEx("hyperliquid", coin),
    if (side == "B") Side.Buy else Side.Sell,
    size,
    originalSize - size,
    price,
    OrdType.Limit,
    OrdStatus.Missing,
    hexBotOrderId ?: "000000"
) {
    val isSpot: Boolean = coin[0] == SPOT_STARTING_CHAR || coin == SPOT_PURR_USDC
}

class HLSpotOrder(
    coin: String,
    side: String,
    @JsonProperty("limitPx") price: Double,
    @JsonProperty("sz") size: Double,
    @JsonProperty("oid") orderId: Long,
    @JsonProperty("origSz") originalSize: Double,
    @JsonProperty("cloid") hexBotOrderId: String? = null
) : HLOrder(coin, side, price, size, orderId, originalSize, hexBotOrderId) {
    override val symbolEx = SymbolEx(
        "hyperliquid-spot",
        HyperliquidSpotConnector.universeCoinToSymbol(coin)
    )
}
//endregion

//region orderbook (for quote)
data class HLOrderBook(
    val coin: String,
    val time: Long,
    val levels: List<List<HLPriceLevel>>
)

data class HLPriceLevel(
    @JsonProperty("px") val price: Double,
    @JsonProperty("sz") val size: Double,
    @JsonProperty("n") val numberOfOrders: Int
)
//endregion

//region rate limits
data class HLUsageStats(
    @JsonProperty("cumVlm") val cumulativeVolume: Double,
    @JsonProperty("nRequestsUsed") val requestsUsed: Int,
    @JsonProperty("nRequestsCap") val requestsCap: Int
)
//endregion

//region balances and positions
data class HLMarginSummaryResponse(
    val marginSummary: HLSummary,
    val crossMarginSummary: HLSummary,
    val crossMaintenanceMarginUsed: String,
    val withdrawable: String,
    val assetPositions: List<HLAssetPosition>,
    val time: Long
)

data class HLSummary(
    val accountValue: Double,
    val totalNtlPos: Double,
    val totalRawUsd: Double,
    val totalMarginUsed: Double
)

data class HLAssetPosition(
    val type: String,
    val position: HLPosition
)

data class HLPosition(
    val coin: String,
    val szi: Double,
    val leverage: HLLeverage,
    val entryPx: Double,
    val positionValue: String,
    val unrealizedPnl: String,
    val returnOnEquity: String,
    val liquidationPx: Double?,
    val marginUsed: String,
    val maxLeverage: Int,
    val cumFunding: HLCumFunding
)

data class HLLeverage(
    val type: String,
    val value: Int
)

data class HLCumFunding(
    val allTime: String,
    val sinceOpen: String,
    val sinceChange: String
)
//endregion

//region supported assets
data class HLAssets(
    val universe: List<HLUniverse>
)

data class HLUniverse(
    val name: String,
    val maxLeverage: Int,
    val szDecimals: Int,
    val isDelisted: Boolean?
)
//endregion

//region candles
class HLCandle(
    t: Long, o: Double, c: Double,
    h: Double, l: Double, v: Double,
) : ExCandle(l, h, o, c, v, Instant.ofEpochMilli(t))
//endregion

data class HLSpotBalancesResponse(val balances: List<HLSpotBalance>) {
    data class HLSpotBalance(
        val coin: String,
        val token: BaseTokenId,
        val hold: Double,
        val total: Double,
        val entryNtl: Double,
    )
}

/**
 * Represents token ID in terms of
 */
typealias BaseTokenId = Int

/**
 * Almost each instrument under "universe" key starts off with this char
 * excepting [SPOT_PURR_USDC].
 */
const val SPOT_STARTING_CHAR: Char = '@'

/**
 * Hyperliquid has uncommon spot instrument named as PURR/USDC under "universe"
 * key whereas others instruments named as `@{index}`.
 */
const val SPOT_PURR_USDC: String = "PURR/USDC"

/**
 * Returns list of spot orders
 */
fun <T : HLOrder> List<T>.spot(): List<T> = this.filter { it.isSpot }

/**
 * Returns list of perpetual orders
 */
fun <T : HLOrder> List<T>.perps(): List<T> = this.filter { !it.isSpot }
