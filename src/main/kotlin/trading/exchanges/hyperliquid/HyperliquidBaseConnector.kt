package trading.exchanges.hyperliquid

import io.micronaut.context.ApplicationContext
import io.micronaut.scheduling.TaskScheduler
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.runBlocking
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import trading.exceptions.*
import trading.exchanges.ApiResponse
import trading.exchanges.ExchangeConnector
import trading.exchanges.checkException
import trading.exchanges.hyperliquid.HLOrderWrapper.Companion.toOrdStatus
import trading.exchanges.hyperliquid.socket.toExecution
import trading.utils.orZero
import trading.models.*
import trading.storage.repos.StrategyRepository
import trading.telegram.TelegramSender
import trading.trading.common.CloseManager
import trading.trading.common.resolveUsdKoef
import trading.utils.scale
import trading.utils.scaleTo
import java.lang.System.currentTimeMillis
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

/**
 * Represents coin name from "universe" key of assets list.
 *
 * For spot assets it is `@{index}` and `PURR/USDC`.
 * For perp assets it is `BTC`, `ETH` and so on.
 */
typealias UniverseCoin = String

abstract class HyperliquidBaseConnector(
    override val name: String,
    apiKey: String,
    protected val hyperLiquidPublicRestClient: HyperLiquidPublicRestClient,
    protected val telegramSender: TelegramSender,
    private val hyperLiquidSigner: HyperLiquidSigner,
    protected val strategyRepository: StrategyRepository,
    protected val wallet: String,
    private val context: ApplicationContext,
    protected val hyperLiquidPrivateRestClient: HyperLiquidPrivateRestClient,
    protected val taskScheduler: TaskScheduler,
): ExchangeConnector() {

    // Enabled only when key is set and strategies with hyper spot are presented
    override var enabled: Boolean = apiKey.isNotEmpty() &&
            strategyRepository.findAll()
                .map { listOf(it.legs.leg1, it.legs.leg2) }
                .flatten()
                .any { it.symbolEx.exchange == name }

    @Volatile
    protected var health = if (enabled) Health.Healthy else Health.Dead

    protected val log: Logger = LoggerFactory.getLogger(this::class.java)

    @Volatile
    protected var limitLeft: String = ""

    // otherwise we get cyclic dependency
    private val closeManager: CloseManager by lazy { context.getBean(CloseManager::class.java) }

    companion object {
        const val LIMITS_THRESHOLDS = 500

        fun getOrderType(
            execInst: List<ExecInst>,
            timeInForce: TimeInForce
        ) = when {
            execInst.contains(ExecInst.ParticipateDoNotInitiate) -> "Alo"
            timeInForce == TimeInForce.ImmediateOrCancel -> "Ioc"
            timeInForce == TimeInForce.GoodTillCancel -> "Gtc"
            else -> error("Unknown order type: $timeInForce")
        }

        fun SuccessResponse.toNewOrder(botOrderId: String) = this.response.data!!.statuses.first()
            .let { // we only support one order per request
                when (it) {
                    is Status.Resting -> NewOrder(botOrderId, exOrderId = it.resting.orderId.toString())
                    is Status.Error -> throw CriticalException(it.error)
                    is Status.Filled -> NewOrder(
                        botOrderId,
                        exOrderId = it.filled.orderId.toString(),
                        filled = it.filled.size,
                        price = it.filled.price
                    )

                    else -> error("Unknown status: $it")
                }
            }
    }

    @PostConstruct
    fun checkRateLimits() {
        if (!enabled) {
            return
        }

        taskScheduler.scheduleCatching(this, 30.seconds.toJavaDuration(), 30.seconds.toJavaDuration()) {
            val limits = runBlocking {
                managed {
                    hyperLiquidPublicRestClient.getRateLimits(wallet)
                }
            }

            limitLeft = "${limits.requestsUsed}/${limits.requestsCap}"

            val left = limits.requestsCap - limits.requestsUsed
            if (left < LIMITS_THRESHOLDS) {
                telegramSender.warnOnce(
                    "HYPERLIQUID_RATE_LIMIT",
                    "Rate limit reached for $name: $left/${limits.requestsCap}, closing"
                )
                strategyRepository.findAll().filter {
                    it.legs.leg1.symbolEx.exchange == name || it.legs.leg2.symbolEx.exchange == name
                }.forEach {
                    closeManager.close(it.uuid, "hyperliquid_exhausted_request_cap")
                }
            }
        }
    }

    final override fun getHealth(): ExHealth = ExHealth(name, health, limitLeft)

    final override suspend fun getExecutions(
        botOrderId: String, exOrderId: String?, symbol: String
    ): List<ExExecution> = managed { hyperLiquidPublicRestClient.getExecutions(wallet) }
        .filter { it.orderId == exOrderId || it.botOrderId == botOrderId }
        .filter { it.liquidation?.liquidatedUser?.endsWith(wallet) != true }
        .map { it.toExecution(name, universeCoinToSymbol(it.coin)) }

    final override suspend fun updateOrder(
        symbol: String,
        botOrderId: String,
        exOrderId: String?,
        size: Double?,
        price: Double?,
        execInst: List<ExecInst>
    ): UpdOrder {
        // you are able to update order, but exchange id will be changed
        // also bot order id will be set to new and old orders :(
        error("Not supported")
    }

    final override fun isUpdateSupported(): Boolean = false

    final override fun contractMultiplier(symbol: String) = 1.0

    final override suspend fun getCandles(symbol: FullSymbol, candles: Short, interval: Interval): List<ExCandle> {
        val resolution = when (interval) {
            Interval.ONE_MINUTE -> 1
            Interval.FIVE_MINUTES -> 5
            Interval.FIFTEEN_MINUTES -> 15
        }

        val now = currentTimeMillis()
        val startTime = now - candles * resolution * 60 * 1000

        return managed {
            hyperLiquidPublicRestClient.getCandles(
                symbolToUniverseCoin(symbol),
                resolution,
                startTime,
                now
            )
        }
            .takeLast(candles.toInt())
            .reversed()
    }

    final override suspend fun getQuote(symbol: FullSymbol): ExQuote =
        managed { hyperLiquidPublicRestClient.getOrderBook(symbolToUniverseCoin(symbol)) }.let {
            ExQuote(
                SymbolEx(name, universeCoinToSymbol(it.coin)),
                it.levels.last().first().price,
                it.levels.first().first().price
            )
        }

    final override suspend fun marketInContracts(
        symbol: FullSymbol,
        size: Double,
        side: Side,
        botOrderId: String,
        execInst: List<ExecInst>
    ): NewOrder = getQuote(symbol)
        .let { quote ->
            try {
                limitInContracts(
                    symbol, size, side,
                    (if (side == Side.Buy) (quote.ask * 1.05) else (quote.bid * 0.95)).scaleTo(quote.ask.scale()),
                    TimeInForce.ImmediateOrCancel,
                    botOrderId, execInst
                ).let { order ->
                    if (size == order.filled) order.copy(closed = true) else order
                }
            } catch (ex: BadGatewayException) {
                throw BadGatewayRollbackException(name)
            }
        }

    final override suspend fun limitInContracts(
        symbol: FullSymbol,
        size: Double,
        side: Side,
        price: Double,
        timeInForce: TimeInForce,
        botOrderId: String,
        execInst: List<ExecInst>
    ): NewOrder {
        val type = getOrderType(execInst, timeInForce)

        val result = managed {
            hyperLiquidPrivateRestClient.limit(
                getAssetId(symbol),
                size,
                side,
                price,
                "0x${botOrderId}",
                type,
                execInst.contains(ExecInst.ReduceOnly) || execInst.contains(ExecInst.Close)
            )
        }

        return (result as SuccessResponse).toNewOrder(botOrderId)
    }

    final override suspend fun cancelOrder(botOrderId: String, exOrderId: String?, symbol: FullSymbol): CanceledOrder {
        val result = try {
            if (exOrderId != null) {
                managed {
                    hyperLiquidPrivateRestClient.cancel(
                        getAssetId(symbol),
                        exOrderId.toLong()
                    )
                }
            } else {
                managed {
                    hyperLiquidPrivateRestClient.cancel(
                        getAssetId(symbol),
                        "0x${botOrderId}"
                    )
                }
            }
        } catch (e: OrderNotFoundException) {
            return CanceledOrder(getOrder(botOrderId, exOrderId, symbol)?.filled ?: 0.0)
        }

        if (result !is SuccessResponse || result.response.data!!.statuses.first() != Status.Success) {
            throw CriticalException("Error cancelling order: $result")
        }

        return CanceledOrder(getOrder(botOrderId, exOrderId, symbol)?.filled ?: 0.0)
    }

    final override fun contractsToUsd(symbol: FullSymbol, contracts: Double): Double =
        strategyRepository.resolveUsdKoef(SymbolEx(name, symbol))
            .let { contracts / it }
            .scaleTo(3)

    final override fun usdToContracts(symbol: FullSymbol, usdSize: Double): Double =
        strategyRepository.resolveUsdKoef(SymbolEx(name, symbol))
            .let { usdSize * it }

    protected suspend fun <T : HLOrder> getOrderInternal(botOrderId: String, exOrderId: String?, symbol: String): FullOrder? =
        kotlin.runCatching {
            val orderWrapper: HLOrderWrapper<T> = managed {
                hyperLiquidPublicRestClient.getOrder<T>(botOrderId, exOrderId, wallet)
            }.order

            val fullOrder = orderWrapper.order
            val status = orderWrapper.status.toOrdStatus()
            // sometimes there's a weird bug when we sent to exchange size = 0.614, for instance,
            // but it creates order with size 0.6136, so we wait for executions after
            // just clearing 'filled' in that case
            val filled = if (status == OrdStatus.Canceled
                && fullOrder.filled < getSupportedInstruments()[symbol]?.min.orZero() * 1.1
            ) {
                0.0
            } else {
                fullOrder.filled
            }
            fullOrder.copy(status = status, filled = filled)
        }.getOrNull()

    protected suspend fun <T : Any> managed(function: suspend () -> ApiResponse<T>): T {
        val response = function.invoke()
        checkException(response.exception, name, telegramSender) { health = Health.Dead }
        return handleResponse(response.body, response.code!!, response.error)
    }

    private fun <T : Any> handleResponse(
        body: T?,
        statusCode: Int,
        rawError: String?
    ): T {

        val (error, code) = if (body is ErrorResponse) {
            body.response to 400
        } else {
            rawError to statusCode
        }

        // most of the responses have 200 http code, so we need to check if error field is not empty
        val apiError = (body as? SuccessResponse)?.response?.data?.statuses?.firstOrNull() as? Status.Error
        if (apiError != null) {
            when {
                apiError.error.startsWith("Order has invalid price") -> throw OrderNotSetException(apiError.error)
                apiError.error.startsWith("Order was never placed") -> throw OrderNotFoundException(apiError.error)
                apiError.error.startsWith("Insufficient margin to place order") -> throw NoMoneyNoHoneyException(name)
                apiError.error.startsWith("Post only order would have immediately") -> throw OrderNotSetException(
                    apiError.error
                )

                apiError.error.startsWith("Order must have minimum value of") -> throw LessThanMinException()
                apiError.error.startsWith("Price must be divisible by tick size") -> {
                    // Refresh supported instruments
                    getSupportedInstruments(refresh = true)
                    throw TickSizeException(apiError.error)
                }

                apiError.error.startsWith("Too many cumulative requests sent") -> {
                    health = Health.Dead
                    throw TooManyRequestsException(name)
                }

                apiError.error.startsWith("Reduce only order would increase position") -> {
                    throw InsufficientCloseAmountException(apiError.error)
                }

                else -> {
                    val err = addSymbolNameToAssetId(apiError.error)
                    telegramSender.warnOnce("HYPERLIQUID_UNKNOWN_ERROR", "Hyperliquid: $err")
                    throw CriticalException(err)
                }
            }
        }

        if (code in 200..299) {
            health = Health.Healthy
            return body ?: throw CriticalException("Response body is null")
        }

        when (code) {
            400 -> {
                if (error?.startsWith("Invalid nonce") == true) {
                    // Invalid nonce: nonce too low 1723055399403 < 1723056662441
                    hyperLiquidSigner.resetNonce()
                    throw CriticalException(error)
                }
                throw CriticalException("Error $code: $error")
            }

            401, 403 -> {
                telegramSender.warnOnce("${name}_BLOCKED", "${name.capitalize()} api denied")
                health = Health.Dead
                throw BotBlockedException("Request forbidden")
            }

            429 -> {
                health = Health.Overloaded

                // TODO implement
                /*pauseUntil = System.currentTimeMillis() + WAIT_TIMEOUT
                //log.error("Too many calls of ${Throwable().stackTrace[2].methodName}")
                 */
                throw TooManyRequestsException(name).also { log.error("Too many calls", it) }
            }

            500 -> {
                health = Health.Dead
                log.error("Error body: $body")
                throw CriticalException("$name error")
            }

            502 -> {
                health = Health.Dead
                throw BadGatewayException()
            }

            503 -> {
                // TODO implement
                //pauseUntil = System.currentTimeMillis() + WAIT_TIMEOUT
                health = Health.Dead
                throw OverloadedException("Overloaded: $error")
            }

            else -> {
                telegramSender.warnIfFrequent("${name}_UNCATCHED", "$error")
                throw CriticalException("${name.capitalize()} uncatched error $code: $error")
            }
        }
    }

    abstract fun addSymbolNameToAssetId(err: String): String

    protected abstract fun symbolToUniverseCoin(symbol: FullSymbol): UniverseCoin

    /**
     * @throws IllegalStateException when no symbol found for coin.
     */
    abstract fun universeCoinToSymbol(coin: UniverseCoin): FullSymbol

    /**
     * Transforms symbol name to asset id
     */
    abstract fun getAssetId(symbol: FullSymbol): Int
}